# 资源聚合方案设计

# 概述

## 项目背景

虚拟电厂平台通过先进信息通信技术和软件系统，实现分布式电源、储能系统、可控负荷、电动汽车等分布式能源资源的聚合和协调优化，作为特殊电厂参与电力市场和电网运行。

## 技术栈

*   后端: JDK8 + Spring Boot 2.x + 融合平台
    
*   前端: Vue 2（2.7.8） + JavaScript + Element UI (2.15.6)+ omega框架 + 融合平台
    
*   其他: Redis、RabbitMQ、Docker
    
*   主备：cluster-assistant
    
*   系统：麒麟2303
    

## 模块功能

### 功能

“资源聚合”模块的核心功能是将分散的“资源”聚合成可用于交易的“机组”。其主要功能点包括：

1.  **机组创建与资源绑定**：
    
    *   用户可以选择机组类型（需求响应、调峰、调频）。
        
    *   创建新机组时，自动展示现有资源列表供用户选择绑定。
        
2.  **机组管理**：
    
    *   查看机组详情
        
    *   修改机组信息
        
    *   删除机组
        
3.  **统计与展示**：
    
    *   机组列表页面会自动统计每个机组绑定的资源数量。
        
    *   机组绑定关系不保留历史记录，监测时仅按最新的绑定关系进行展示。
        

### 业务规则

1.  **资源绑定规则**：
    
    *   **调频机组**：只能绑定**直控**型资源，且通常以同一个并网节点进行聚合。
        
    *   **调峰机组**：通常以同一个并网节点进行聚合。
        
    *   **需求响应机组**：通常以资源所在地同一个地市进行聚合。
        
2.  机组类型不能多选，已经绑定资源的机组不能修改类型，未绑定的可以修改。
    
3.  同一资源可以被不同类型机组绑定，不能被同类型机组绑定。
    

### 功能流程

```mermaid
graph TD
    A[开始] --> B{选择机组类型};

    B -- 调频 --> C[创建调频机组];
    B -- 调峰/需求响应 --> D[创建调峰/需求响应机组];

    C --> E{绑定资源<br>过滤非直控型资源}
    D --> F{绑定资源<br>展示已建立资源列表}

    E --> G[机组列表页面<br>自动统计绑定的资源数量];
    F --> G;

    G --> H{选择操作};
    H -- 查看详情 --> I[机组详情<br>展示已绑定资源列表];
    I -- 解绑资源 --> P[确认解绑并更新绑定关系];
    H -- 修改机组 --> J[修改机组];
    J --> K[展示未被本类型机组绑定的所有资源列表];
    K --> L[更新绑定关系];
    H -- 删除机组 --> N[删除机组];
    N --> O[结束];

```

# 数据结构设计

### 设计思路

**多对多关系**：一个“机组”可以聚合多个“资源”，一个“资源”也可能被不同类型的机组所用。因此，“机组”和“资源”之间是典型的多对多关系，需要一个中间表（`unit_resource`）来连接它们。

### 机组列表（Unit）

| 字段名 | 类型 | 长度 | 必填 | 必填 |
| --- | --- | --- | --- | --- |
| id | bigint | \- | 是 | 主键ID |
| unit\_id | bigint | \- | 是 | 机组ID |
| user\_id | bigint | \- | 是 | 用户ID |
| unit\_name | varchar | 50 | 是 | 机组名称 |
| unit\_type | varchar | 20 | 是 | 机组类型 |

**机组类型**

*   demand\_response(需求响应)
    
*   peak\_shaving(调峰)
    
*   frequency\_regulation(调频)
    

### 机组-资源关联详情 (Unit\_Resource)

| 字段名 | 类型 | 长度 | 必填 | 必填 |
| --- | --- | --- | --- | --- |
| unit\_id | bigint | \- | 是 | 机组ID（外键） |
| resource\_id | varchar | 50 | 是 | 资源ID（外键） |

# 接口设计

### 基础路径

`/api/v1`

### 响应格式

```json
{
  "code": 200,
  "message": "success",
  "data": {},
  "timestamp": "2025-06-17T10:30:00"
}
```

### 核心接口设计

| 说明 | 路径 | 方法 |
| --- | --- | --- |
| 创建一个新的机组，并同时绑定指定的资源。 | /api/v1/resource/agg/units | POST |
| 删除一个机组。删除后，其与资源的绑定关系也一并解除。 | /api/v1/resource/agg/units/{unitId} | DELETE |
| 更新指定机组的信息，包括名称和绑定的资源列表。 | /api/v1/resource/agg/units/{unitId} | POST |
| 根据机组ID获取其详细信息，包括绑定的所有资源列表。 | /api/v1/resource/agg/units/{unitId} | GET |
| 查询机组列表 | /api/v1/resource/agg/units | GET |
| 在创建或修改机组时，根据机组类型等规则，获取当前可供选择绑定的资源列表。 | /api/v1/resource/agg/available-resources | GET |
| 将一个指定的资源从机组中解绑。 | /api/v1/resource/agg/units/{unitId}/resources/{resourceId} | DELETE |
| 获取所有资源所属区域的枚举列表，用于前端筛选。 | /api/v1/resource/agg/regions | GET |

#### 创建新机组

*   **功能描述**：创建一个新的机组，并同时绑定指定的资源。
    
*   **方法**：POST
    
*   **路径**： `/api/v1/resource/agg/units`
    
*   **请求体 (Body)**：
    

```json
{
  "unit_name": "华南区-调峰机组A",
  "unit_type": "peak_shaving",
  "resource_ids": ["resource_id_01", "resource_id_02", "resource_id_03"]
}

```

*   **响应数据 (data)**
    

*   **核心后端逻辑**
    

1.  **接收并验证参数**：接收 `POST` 请求体中的 `unit_name`, `unit_type`, 和 `resource_ids`。验证 `unit_name` 和 `unit_type` 的有效性（如非空、格式正确）。
    
2.  **开启事务**：启动数据库事务，确保数据创建的原子性。
    
3.  **创建机组实体**：在 `Unit` 表中插入一条新记录，包含 `unit_name`, `unit_type`, 以及从用户会话（token）中获取的 `user_id`。获取新生成的主键 `unit_id`。
    
4.  **创建关联关系**：如果 `resource_ids` 列表不为空，则遍历该列表，将每条 `[unit_id, resource_id]` 的对应关系插入到 `unit_resource` 中间表中。
    
5.  **提交事务**：如果以上所有步骤都成功，提交事务。如果任何步骤失败，则回滚事务，防止产生脏数据。
    
6.  **返回响应**：返回成功的响应（空的 data 或包含新机组 ID 的信息）。若验证失败或数据库操作失败，则返回相应的错误信息。
    

#### 删除机组

*   **功能描述**：删除一个机组。删除后，其与资源的绑定关系也一并解除。
    
*   **方法**：DELETE
    
*   **路径**： `/api/v1/resource/agg/units/{unitId}`
    
*   **请求参数 (Path)**：
    

| 参数名 | 类型 | 必填 | 描述 |
| --- | --- | --- | --- |
| unitId | int | 是 | 机组的唯一ID |

*   **响应数据 (data)**：
    

*   **核心后端逻辑**
    

1.  **接收参数**：从 URL 路径中获取 `unitId`。
    
2.  **开启事务**：启动数据库事务。
    
3.  **删除关联关系**：根据 `unitId`，首先删除 `unit_resource` 中间表中所有相关的资源绑定记录。
    
4.  **删除机组实体**：在 `unit_resource` 中的记录被清除后，根据 `unitId` 删除 `Unit` 表中的机组记录。
    
5.  **提交或回滚事务**：提交事务以永久删除数据，或在出错时回滚。
    
6.  **返回响应**：返回一个空的成功响应（HTTP 状态码 200 ）。
    

#### 编辑机组信息

*   **功能描述**：更新指定机组的信息，包括名称和绑定的资源列表。
    
*   **方法**：POST
    
*   **路径**： `/api/v1/resource/agg/units/{unitId}`
    
*   **请求参数 (Path)**：
    

| 参数名 | 类型 | 必填 | 描述 |
| --- | --- | --- | --- |
| unitId | int | 是 | 机组的唯一ID |

*   **请求体 (Body)**：
    

```json
{
  "unit_name": "华南区-调峰机组A-修改后",
  "resource_ids": ["resource_id_01", "resource_id_04"]
}

```

*   **响应数据 (data)**：
    

```json
{
  "unit_id": "1234567891",
  "unit_name": "华南区-调峰机组A-修改后",
  "unit_type": "peak_shaving",
  "resource_ids": ["resource_id_01", "resource_id_04"]
}

```

*   **核心后端逻辑**
    

1.  **接收参数**：从 URL 路径中获取 `unitId`，从请求体中获取 `unit_name` 和 `resource_ids`。
    
2.  **开启事务**：为保证数据一致性，启动数据库事务。
    
3.  **更新机组主信息**：根据 `unitId` 在 `Unit` 表中找到对应记录，并将其 `unit_name` 更新为请求中提供的新名称。
    
4.  **更新资源绑定关系**：
    

*   如果 `resource_ids` 列表不为空，则遍历该列表，将每条 `[unit_id, resource_id]` 的对应关系插入到 `unit_resource` 中间表中。
    

1.  **提交或回滚事务**：如果所有操作成功，则提交事务。否则，回滚所有更改。
    
2.  **返回响应**：返回更新后的机组信息或一个成功的确认消息。
    

#### 获取机组详情

*   **功能描述**：根据机组ID获取其详细信息，包括绑定的所有资源列表。
    
*   **方法**：GET
    
*   **路径**： `/api/v1/resource/agg/units/{unitId}`
    
*   **请求参数 (Path)**：
    

| 参数名 | 类型 | 必填 | 描述 |
| --- | --- | --- | --- |
| unitId | int | 是 | 机组的唯一ID |

*   **响应数据 (data)**：
    

```json
{
  "unit_id": "1234567891",
  "unit_name": "华南区-调峰机组A",
  "unit_type": "peak_shaving",
  "user_id": "user002",
  "bound_resources": [
    {
      "resource_id": "resource_id_01",
      "resource_name": "储能站A"
    },
    {
      "resource_id": "resource_id_02",
      "resource_name": "可调节负荷B"
    }
  ]
}

```

*   **核心后端逻辑**
    

1.  **接收参数**：从 URL 路径中获取 `unitId`。
    
2.  **查询机组主信息**：根据 `unitId` 在 `Unit` 表中查询对应的机组信息。如果未找到，返回错误信息。
    
3.  **查询绑定的资源**：
    

*   使用 `unitId` 在 `unit_resource` 中间表中查找所有关联的 `resource_id`。
    
    *   通过 `JOIN` 操作，关联 `Resource` 表（资源主表），以获取每个 `resource_id` 对应的 `resource_name` 等详细信息。
        

1.  **组装数据**：将查询到的机组主信息和其绑定的资源列表组合成指定的 JSON 结构。
    
2.  **返回响应**：将组装好的数据作为响应体返回。
    

#### 获取机组列表

*   **功能描述**：查询机组列表
    
*   **方法**：GET
    
*   **路径**： `/api/v1/resource/agg/units`
    
*   **请求参数 (Query)**：
    

| 参数名 | 类型 | 必填 | 描述 |
| --- | --- | --- | --- |
| unit\_type | string | 否 | 机组类型 (demand\_response, peak\_shaving, frequency\_regulation) |

*   **响应数据 (data)**：
    

```json
{
  "total": 100,
  "list": [
    {
      "unit_id": "1234567890",
      "unit_name": "华东区-需求响应机组01",
      "unit_type": "demand_response",
      "resource_count": 15,
      "user_id": "user001"
    }
  ]
}

```

*   **核心后端逻辑**
    

1.  **接收请求**：接收前端发送的 `GET` 请求，获取可选的查询参数 `unit_type`。
    
2.  **构建查询**：
    

*   以 `Unit` 表为主表，构建基础查询。
    
    *   如果 `unit_type` 参数存在，则在查询中加入 `WHERE unit_type = ?` 的条件进行过滤。
        

1.  **统计资源数量**：
    

*   使用 `LEFT JOIN` 连接 `unit_resource` 中间表（`ON Unit.unit_id = unit_resource.unit_id`）。
    
    *   使用 `GROUP BY Unit.unit_id` 对每个机组进行分组。
        
    *   使用 `COUNT(unit_resource.resource_id)` 计算每个机组下绑定的资源数量，并将其命名为 `resource_count`。
        

1.  **分页与排序**：为了性能和更好的用户体验，应加入分页逻辑（例如，接收 `page` 和 `pageSize` 参数），并按创建时间或机组名称等进行排序。
    
2.  **执行查询**：执行构建好的 SQL 查询，同时查询总条目数（用于分页）和当前页的数据列表。
    
3.  **格式化响应**：将查询结果组装成指定的 JSON 格式，包含 `total` 和 `list` 字段，然后返回给前端。
    

#### 获取可绑定的资源列表

*   **功能描述**：在创建或修改机组时，根据机组类型等规则，获取当前可供选择绑定的资源列表。
    
*   **方法**：GET
    
*   **路径**： `/api/v1/resource/agg/available-resources`
    
*   **请求参数 (Query)**：
    

| 参数名 | 类型 | 必填 | 描述 |
| --- | --- | --- | --- |
| unit\_type | string | 是 | 机组类型，用于过滤不符合绑定规则的资源。 |
| unit\_id | int | 否 | （可选）在修改机组时传入，用于排除已被本机组绑定的资源。 |
| region | string | 是 | 资源所属区域 |

*   **响应数据 (data)**：
    

```json
{
    "list": [
        {
            "resource_id": "resource_id_01",
            "resource_name": "储能站A"
        },
        {
            "resource_id": "resource_id_02",
            "resource_name": "可调节负荷B"
        }
    ]
}

```

*   **核心后端逻辑**
    

1.  **接收参数**：获取必要的 `unit_type` 和可选的 `unit_id` 参数。
    
2.  **构建基础查询**：从 `Resource` 表（资源主表）开始构建查询。
    
3.  **应用规则过滤**：
    

*   根据 `unit_type` 添加 `WHERE` 条件。例如，若 `unit_type` 为 `frequency_regulation`，则添加条件 `WHERE platform_direct_control = true`。
    

1.  **排除已绑定资源**：
    

*   查询 `unit_resource` 表，找出已经被绑定的 `resource_id`，排除这些已被占用的资源。
    
    *   如果传入了 `unit_id`（表示正在编辑），此时应排除已绑定到当前 `unit_id` 的资源。
        

1.  **执行查询并返回**：执行查询，获取符合条件的资源列表（`resource_id`, `resource_name`），并按指定格式返回。
    

#### 从机组解绑单个资源（删除）

*   **功能描述**：将一个指定的资源从机组中解绑。
    
*   **方法**：`DELETE`
    
*   **路径**： `/api/v1/resource/agg/units/{unitId}/resources/{resourceId}`
    
*   **请求参数 (Path)**：
    

| 参数名 | 类型 | 必填 | 描述 |
| --- | --- | --- | --- |
| unitId | int | 是 | 机组的唯一ID |
| resourceId | string | 是 | 要解绑的资源的唯一ID |

*   **核心后端逻辑**
    

1.  **接收参数**：从 URL 路径中获取 `unitId` 和 `resourceId`。
    
2.  **执行删除**：在 `unit_resource` 中间表上执行 `DELETE` 操作。
    
3.  **设置删除条件**：`WHERE` 子句必须同时匹配 `unit_id = ?` 和 `resource_id = ?`，以确保只删除指定的单一绑定关系。
    
4.  **返回响应**：执行成功后，返回一个空的成功响应（HTTP 200 ）。
    

#### 获取资源区域枚举值

功能描述：获取所有资源所属区域的枚举列表，用于前端界面的筛选。

方法：`GET`

路径： `/api/v1/resource/agg/regions`

请求参数 (Query)：无

响应数据 (data)：

```json
 {
   "list": [
     {
       "district": "广州市"
     }
   ]
 }
```

核心后端逻辑

接收请求：接收 `GET` 请求。

构建查询：

以 `Resource` 表（资源主表）为查询目标。

使用 `SELECT DISTINCT` 来查询所有不重复的区域信息（district）

# 页面设计

### 项目结构

整个虚拟电厂项目作为一个插件，放[Solutions/BIZ-能源管理/\_git/energy-fusion-web](https://cetsoft-svr1/Solutions/BIZ-%E8%83%BD%E6%BA%90%E7%AE%A1%E7%90%86/_git/energy-fusion-web)下统一管理

当前开发的资源管理作为虚拟电厂插件下的一个模块，放到src\projects\vpp-resource-manager中。

### 组件拆分

组件路径：src\projects\vpp-resource-manager\aggregation\

*   机组列表组件（主入口）：index.vue
    
*   新增/编辑机组：components\AddUnitDialog.vue
    
*   机组详情：components\DetailDrawer.vue
    

### 组件库使用

eem-base + element UI + cet-common中的（cet-dateSelect、cet-aside、cet-gaintTree）

> 关于cet-common组件，张翔建议只使用cet-dateSelect、cet-aside、cet-gaintTree，其他一律使用 element v2 中的组件。

### 其他

*   样式使用变量（需要适配不同的皮肤），充分利用已有的自定义样式；
    
*   国际化
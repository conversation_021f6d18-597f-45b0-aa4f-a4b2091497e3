<template>
  <el-dialog
    :title="dialogTitle"
    :visible.sync="dialogVisible"
    width="960px"
    :before-close="handleClose"
    class="add-unit-dialog"
    append-to-body
  >
    <div class="dialog-content">
      <!-- 基本信息表单 -->
      <div class="form-section">
        <el-form
          ref="addUnitForm"
          :model="formData"
          :rules="formRules"
        
        >
          <div class="form-row">
            
              <el-form-item :label="$T('机组名称')" prop="unitName">
                <el-input
                  v-model="formData.unitName"
                  :placeholder="$T('请输入内容')"
                  class="form-input"
                />
              </el-form-item>
              <el-form-item :label="$T('机组类型')" prop="unitType">
                <el-select
                  v-model="formData.unitType"
                  class="form-select"
                  :placeholder="$T('请选择机组类型')"
                  :disabled="mode === 'edit'"
                >
                  <el-option
                    v-for="option in unitTypeOptions"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </el-form-item>
           
          </div>

          <!-- 机组类型提示信息 -->
          <div v-if="formData.unitType && unitTypeHint" class="unit-type-hint">
            <el-alert
              :title="unitTypeHint"
              type="info"
              :closable="false"
              show-icon
            />
          </div>
        </el-form>
      </div>

      <!-- 资源列表区域 -->
      <div v-if="formData.unitType" class="resource-section">
        <div class="section-title">{{ resourceListTitle }}</div>
        
        <!-- 搜索和筛选 -->
        <div class="resource-filter">
          <div class="filter-left">
            <el-input
              v-model="resourceSearch"
              :placeholder="$T('请输入关键字')"
              prefix-icon="el-icon-search"
              class="search-input"
            />
            <CustomElSelect
              v-model="selectedArea"
              :prefix_in="$T('区域')"
              class="area-select"
            >
              <el-option
                v-for="option in areaOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </CustomElSelect>
          </div>
        </div>

        <!-- 资源表格 -->
        <div class="resource-table">
          <el-table
            ref="resourceTable"
            :data="currentResourceData"
            @selection-change="handleSelectionChange"
            max-height="440"
          >
            <el-table-column
              type="selection"
              width="55"
              align="center"
            />
            <el-table-column
              prop="index"
              :label="$T('序号')"
              width="80"
              align="center"
            >
              <template slot-scope="scope">
                {{ String(scope.row.index).padStart(2, '0') }}
              </template>
            </el-table-column>
            <el-table-column
              prop="resourceId"
              :label="$T('资源ID')"
              min-width="180"
            />
            <el-table-column
              prop="resourceName"
              :label="$T('资源名称')"
              min-width="150"
            />
            <el-table-column
              prop="area"
              :label="$T('区域')"
              min-width="100"
            />
            <el-table-column
              prop="capacity"
              :label="$T('报装容量（kVA）')"
              min-width="140"
            />
            <el-table-column
              prop="directControl"
              :label="$T('平台直控')"
              min-width="100"
              align="center"
            >
              <template slot-scope="scope">
                {{ scope.row.directControl ? $T('是') : $T('否') }}
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 分页 -->
        <div class="resource-pagination">
          <div class="pagination-controls">
            <div class="pagination-info">
              <span class="total-count">
                {{ $T('共') }}<span class="count-number">{{ resourceTotalCount }}</span>{{ $T('个') }}
              </span>
            </div>

            <div class="page-size-selector">
              <el-select v-model="resourcePageSize" class="page-size-select" @change="handlePageSizeChange">
                <el-option
                  v-for="size in pageSizeOptions"
                  :key="size"
                  :label="`${size}${$T('条/页')}`"
                  :value="size"
                />
              </el-select>
            </div>

            <el-pagination
              :current-page="resourceCurrentPage"
              :page-size="resourcePageSize"
              :total="resourceTotalCount"
              layout="prev, pager, next, jumper"
              @current-change="handleResourcePageChange"
              class="pagination-component"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 底部按钮 -->
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">{{ $T('取消') }}</el-button>
      <el-button type="primary" @click="handleConfirm">{{ $T('确定') }}</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'AddUnitDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    mode: {
      type: String,
      default: 'add',
      validator: value => ['add', 'edit'].includes(value)
    },
    editData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      dialogVisible: false,
      
      // 表单数据
      formData: {
        unitName: '',
        unitType: ''
      },

      // 表单验证规则
      formRules: {
        unitName: [
          { required: true, message: this.$T('请输入机组名称'), trigger: 'blur' },
          { min: 1, max: 50, message: this.$T('长度在 1 到 50 个字符'), trigger: 'blur' }
        ],
        unitType: [
          { required: true, message: this.$T('请选择机组类型'), trigger: 'change' }
        ]
      },
      
      // 机组类型选项
      unitTypeOptions: [
        { label: this.$T('调峰机组'), value: 'peak' },
        { label: this.$T('调频机组'), value: 'frequency' },
        { label: this.$T('需求响应机组'), value: 'response' },
      ],
      
      // 资源搜索和筛选
      resourceSearch: '',
      selectedArea: 'all',
      areaOptions: [
        { label: this.$T('全部'), value: 'all' },
        { label: '华北区域', value: 'north' },
        { label: '华东区域', value: 'east' },
        { label: '华南区域', value: 'south' },
        { label: '西北区域', value: 'northwest' }
      ],
      
      // 资源列表数据
      allResourceData: [],
      selectedResources: [],
      
      // 分页
      resourceCurrentPage: 1,
      resourcePageSize: 10,
      pageSizeOptions: [10, 20, 50, 100]
    };
  },
  computed: {
    // 弹窗标题
    dialogTitle() {
      return this.mode === 'edit' ? this.$T('编辑') : this.$T('新增');
    },

    // 资源列表标题
    resourceListTitle() {
      if (this.formData.unitType) {
        const selectedOption = this.unitTypeOptions.find(option => option.value === this.formData.unitType);
        const listType = this.mode === 'edit' ? this.$T('剩余资源列表') : this.$T('资源列表');
        return selectedOption ? `${selectedOption.label}${listType}` : `${this.$T('机组')}${listType}`;
      }
      return this.mode === 'edit' ? this.$T('机组剩余资源列表') : this.$T('机组资源列表');
    },

    // 筛选后的数据
    filteredResourceData() {
      let filtered = this.getAvailableResourceData();

      // 关键字搜索筛选
      if (this.resourceSearch.trim()) {
        const keyword = this.resourceSearch.trim().toLowerCase();
        filtered = filtered.filter(item =>
          item.resourceName.toLowerCase().includes(keyword) ||
          item.resourceId.toLowerCase().includes(keyword) ||
          item.area.toLowerCase().includes(keyword)
        );
      }

      // 区域筛选
      if (this.selectedArea && this.selectedArea !== 'all') {
        filtered = filtered.filter(item => item.areaValue === this.selectedArea);
      }

      return filtered;
    },

    currentResourceData() {
      const start = (this.resourceCurrentPage - 1) * this.resourcePageSize;
      const end = start + this.resourcePageSize;
      return this.filteredResourceData.slice(start, end);
    },

    resourceTotalCount() {
      return this.filteredResourceData.length;
    },

    // 机组类型提示信息
    unitTypeHint() {
      switch (this.formData.unitType) {
        case 'frequency':
          return this.$T('调频机组只能绑定直控型的资源，一般以同一个并网节点聚合');
        case 'peak':
          return this.$T('调峰机组一般以同一个并网节点聚合');
        case 'response':
          return this.$T('需求响应机组一般以资源所在地同一个地市聚合');
        default:
          return '';
      }
    }
  },
  watch: {
    visible: {
      handler(val) {
        console.log('AddUnitDialog visible prop changed:', val);
        this.dialogVisible = val;
        if (val) {
          this.initDialog();
        }
      },
      immediate: true
    },

    mode: {
      handler() {
        if (this.dialogVisible) {
          this.initDialog();
        }
      }
    },

    editData: {
      handler() {
        if (this.dialogVisible && this.mode === 'edit') {
          this.initDialog();
        }
      },
      deep: true
    },

    dialogVisible(val) {
      console.log('AddUnitDialog dialogVisible changed:', val);
      this.$emit('update:visible', val);
    },

    // 监听搜索关键字变化
    resourceSearch() {
      this.resourceCurrentPage = 1; // 重置到第一页
    },

    // 监听区域筛选变化
    selectedArea() {
      this.resourceCurrentPage = 1; // 重置到第一页
    }
  },
  created() {
    console.log('AddUnitDialog created');
    this.generateResourceMockData();
  },
  mounted() {
    console.log('AddUnitDialog mounted, visible prop:', this.visible);
  },
  methods: {
    // 初始化弹窗
    initDialog() {
      if (this.mode === 'edit' && this.editData) {
        // 编辑模式：使用传入的数据
        this.formData = {
          unitName: this.editData.unitName || '',
          unitType: this.editData.unitType || ''
        };
        // 编辑模式下，设置已选资源
        this.selectedResources = this.editData.selectedResources || [];
        // 在下一个tick中设置表格的选中状态
        this.$nextTick(() => {
          this.setTableSelection();
        });
      } else {
        // 新增模式：使用空数据
        this.formData = {
          unitName: '',
          unitType: ''
        };
        this.selectedResources = [];
      }

      this.resourceSearch = '';
      this.selectedArea = 'all';
      this.resourceCurrentPage = 1;

      // 清除表单验证
      this.$nextTick(() => {
        if (this.$refs.addUnitForm) {
          this.$refs.addUnitForm.clearValidate();
        }
      });
    },

    // 设置表格选中状态（编辑模式下使用）
    setTableSelection() {
      if (this.$refs.resourceTable && this.selectedResources.length > 0) {
        // 清除当前选中状态
        this.$refs.resourceTable.clearSelection();
        // 设置已选资源的选中状态
        this.currentResourceData.forEach(row => {
          const isSelected = this.selectedResources.some(selected => selected.resourceId === row.resourceId);
          if (isSelected) {
            this.$refs.resourceTable.toggleRowSelection(row, true);
          }
        });
      }
    },
    
    // 生成资源Mock数据
    generateResourceMockData() {
      const data = [];
      const areaConfigs = [
        { label: '华北区域', value: 'north' },
        { label: '华东区域', value: 'east' },
        { label: '华南区域', value: 'south' },
        { label: '西北区域', value: 'northwest' }
      ];

      for (let i = 1; i <= 90; i++) {
        const areaConfig = areaConfigs[Math.floor(Math.random() * areaConfigs.length)];
        data.push({
          index: i,
          resourceId: `9144030078525478X${i}`,
          resourceName: `资源${i}`,
          area: areaConfig.label,
          areaValue: areaConfig.value,
          capacity: Math.floor(Math.random() * 1000) + 100, // 100-1100 kVA
          directControl: Math.random() > 0.5 // 随机生成是否平台直控
        });
      }
      this.allResourceData = data;
    },

    // 获取可用资源数据（编辑模式下排除已选资源，但保留当前编辑项的已选资源）
    getAvailableResourceData() {
      let availableData = [];

      if (this.mode === 'edit' && this.editData && this.editData.selectedResources) {
        // 编辑模式：显示剩余资源 + 当前已选资源
        const currentSelectedIds = this.editData.selectedResources.map(item => item.resourceId);
        availableData = this.allResourceData.filter(item =>
          !this.isResourceUsedByOthers(item.resourceId) || currentSelectedIds.includes(item.resourceId)
        );
      } else {
        // 新增模式：显示所有未被使用的资源
        availableData = this.allResourceData.filter(item => !this.isResourceUsedByOthers(item.resourceId));
      }

      // 调频机组只能绑定直控型资源
      if (this.formData.unitType === 'frequency') {
        availableData = availableData.filter(item => item.directControl === true);
      }

      return availableData;
    },

    // 检查资源是否被其他机组使用（这里用mock数据模拟）
    isResourceUsedByOthers(resourceId) {
      // 这里应该调用实际的API来检查资源是否被其他机组使用
      // 现在用mock数据模拟：假设ID末尾为偶数的资源已被其他机组使用
      const idNumber = parseInt(resourceId.slice(-1));
      return idNumber % 2 === 0;
    },
    
    // 表格选择变化
    handleSelectionChange(selection) {
      this.selectedResources = selection;
    },
    
    // 资源分页变化
    handleResourcePageChange(page) {
      this.resourceCurrentPage = page;
      // 编辑模式下，分页变化后重新设置选中状态
      if (this.mode === 'edit') {
        this.$nextTick(() => {
          this.setTableSelection();
        });
      }
    },

    // 页面大小变化
    handlePageSizeChange(size) {
      this.resourcePageSize = size;
      this.resourceCurrentPage = 1; // 重置到第一页
      // 编辑模式下，页面大小变化后重新设置选中状态
      if (this.mode === 'edit') {
        this.$nextTick(() => {
          this.setTableSelection();
        });
      }
    },
    
    // 关闭弹窗
    handleClose() {
      this.dialogVisible = false;
    },
    
    // 取消
    handleCancel() {
      this.dialogVisible = false;
    },
    
    // 确定
    handleConfirm() {
      // 表单验证
      this.$refs.addUnitForm.validate((valid) => {
        if (valid) {
          // 提交数据
          const submitData = {
            ...this.formData,
            selectedResources: this.selectedResources
          };

          if (this.mode === 'edit') {
            this.$emit('update', submitData);
            this.$message.success(this.$T('编辑成功'));
          } else {
            this.$emit('confirm', submitData);
            this.$message.success(this.$T('新增成功'));
          }

          this.dialogVisible = false;
        } else {
          this.$message.warning(this.$T('请完善表单信息'));
          return false;
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.add-unit-dialog {
  // 弹窗整体居中
  :deep(.el-dialog) {
    margin-top: 5vh !important;
  }

  // ::v-deep .el-dialog__wrapper {
  //   display: flex;
  //   align-items: center;
  //   justify-content: center;
  // }
  .dialog-content {
    @include padding(J4);
    
    .form-section {
      .form-row {
        display: flex;
        gap: var(--J4);
      }

      .unit-type-hint {
        @include margin_top(J3);
      }
    }
    
    .resource-section {
      .section-title {
        @include font_size(Aa);
        @include font_color(T1);
        @include margin_bottom(J2);
      }
      
      .resource-filter {
        display: flex;
        justify-content: space-between;
        align-items: center;
        @include margin_bottom(J3);
        
        .filter-left {
          display: flex;
          gap: var(--J3);
          
          .search-input {
            width: 240px;
          }
          
          .area-select {
            width: 240px;
          }
        }
        
        .bind-btn {
          opacity: 0;
        }
      }
      
      .resource-table {
        @include margin_bottom(J3);
      }
      
      .resource-pagination {
        display: flex;
        justify-content: flex-end;
        align-items: center;

        .pagination-controls {
          display: flex;
          align-items: center;
          gap: var(--J3);

          .pagination-info {
            .total-count {
              @include font_color(T2);
              @include font_size(Aa);

              .count-number {
                @include font_color(ZS);
              }
            }
          }

          .page-size-selector {
            .page-size-select {
              width: 100px;
            }
          }
        }
      }
    }
  }
}
</style>
